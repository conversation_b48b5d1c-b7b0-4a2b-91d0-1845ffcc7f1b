from __future__ import annotations

"""To-Dos CRUD blueprint.

All endpoints are authenticated and scoped to the single user. Completed
todos are automatically cleaned up after 24 hours via MongoDB TTL index.
"""

from datetime import datetime, timezone
from typing import Any, Dict, List

from bson import ObjectId
from flask import Blueprint, jsonify, request
from flask_login import current_user, login_required
from werkzeug.exceptions import BadRequest, NotFound

from app.models.todo import Todo

bp = Blueprint("todos", __name__, url_prefix="/api/todos")


# ---------------------------------------------------------------------------
# Helpers
# ---------------------------------------------------------------------------


def _json_error(msg: str, code: int = 400):
    return jsonify({"error": msg}), code


# ---------------------------------------------------------------------------
# Routes
# ---------------------------------------------------------------------------


@bp.get("/")
@login_required
def list_todos():
    todos: List[Todo] = Todo.list_by_user(current_user.id)
    return jsonify({"todos": [todo.to_dict() for todo in todos]})


@bp.post("/")
@login_required
def create_todo():
    if not request.is_json:
        raise BadRequest("Request must be JSON")

    data: Dict[str, Any] = request.get_json(silent=True) or {}
    text = data.get("text", "").strip()
    if not text:
        return _json_error("Text is required")

    todo = Todo.create(user_id=current_user.id, text=text)
    return jsonify({"todo": todo.to_dict()}), 201


@bp.patch("/<todo_id>")
@login_required
def update_todo(todo_id: str):
    todo = Todo.get(todo_id)
    if todo is None:
        raise NotFound("Todo not found")

    if str(todo._doc["user_id"]) != current_user.id:
        return _json_error("Forbidden", 403)

    data = request.get_json(silent=True) or {}
    update_fields: Dict[str, Any] = {}

    if "text" in data:
        text = data["text"].strip()
        if not text:
            return _json_error("Text cannot be empty")
        update_fields["text"] = text

    if "completed" in data:
        if data["completed"] and not todo._doc.get("completed_at"):
            # Set completion time only if it's not already completed
            update_fields["completed_at"] = datetime.utcnow().replace(tzinfo=timezone.utc)
        else:
            # Allow un-completing the item
            update_fields["completed_at"] = None

    if update_fields:
        todo.update(**update_fields)

    return jsonify({"todo": todo.to_dict()})


@bp.delete("/<todo_id>")
@login_required
def delete_todo(todo_id: str):
    todo = Todo.get(todo_id)
    if todo is None:
        raise NotFound("Todo not found")
    if str(todo._doc["user_id"]) != current_user.id:
        return _json_error("Forbidden", 403)

    todo.delete()
    return {"status": "deleted"}, 204
