from __future__ import annotations

"""Datetime utilities for the Personal Organizer application.

Provides common datetime parsing and formatting functions used across
the application, particularly for handling ISO 8601 datetime strings
and timezone conversions.
"""

from datetime import datetime, timezone

import dateutil.parser


def parse_iso8601(value: str) -> datetime:
    """Parse an ISO 8601 datetime string into a timezone-aware UTC datetime object.

    Args:
        value: ISO 8601 formatted datetime string

    Returns:
        datetime: Timezone-aware UTC datetime object

    Raises:
        ValueError: If the string cannot be parsed
    """
    try:
        dt = dateutil.parser.isoparse(value)
        if dt.tzinfo is not None:
            # If timezone-aware, convert to UTC
            return dt.astimezone(timezone.utc)
        # If naive, assume it's already UTC and make it aware
        return dt.replace(tzinfo=timezone.utc)
    except (ValueError, TypeError) as e:
        raise ValueError(f"Invalid ISO 8601 datetime: {value}") from e


def format_iso8601(dt: datetime) -> str:
    """Format a datetime object as an ISO 8601 string.

    Args:
        dt: datetime object (assumed to be UTC)

    Returns:
        str: ISO 8601 formatted string
    """
    return dt.isoformat() + 'Z'
