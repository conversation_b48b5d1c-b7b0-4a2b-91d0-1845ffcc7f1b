import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";
import { loadEnv } from "vite";
import path from "path";
import { fileURLToPath, URL } from "node:url";

// ---------------------------------------------------------------------------
// Vite configuration
// ---------------------------------------------------------------------------

export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd(), "");

  return {
    plugins: [react()],

    server: {
      port: 5173,
      proxy: {
        // Proxy all /api requests to the Flask backend
        // This ensures frontend and backend appear to be on the same origin for cookies
        "/api": {
          target: env.VITE_BACKEND_URL || "http://127.0.0.1:8001",
          changeOrigin: true,
          secure: false,
          rewrite: (path) => path.replace(/^\/api/, '/api'), // Keep /api prefix for Flask routes
        },
      },
    },

    build: {
      outDir: "dist",
      sourcemap: mode === "development",
    },

    // Enable absolute imports with @/** syntax
    resolve: {
      alias: {
        "@": fileURLToPath(new URL("./src", import.meta.url)),
      },
    },

    css: {
      postcss: {
        plugins: [
          // autoprefixer is included via postcss.config.js
        ],
      },
    },
  };
});
